"""
辅助工具函数
"""
import logging
import sys
from pathlib import Path
from typing import Optional

from config import LOG_LEVEL, LOG_FORMAT


def setup_logging(level: str = None, log_file: Optional[Path] = None) -> None:
    """设置日志配置"""
    level = level or LOG_LEVEL
    
    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(LOG_FORMAT))
        logging.getLogger().addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('github').setLevel(logging.WARNING)


def validate_github_token(token: Optional[str]) -> bool:
    """验证GitHub token"""
    if not token:
        return False
    
    # 简单的token格式验证
    if len(token) < 20:
        return False
    
    return True


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def truncate_string(text: str, max_length: int = 50) -> str:
    """截断字符串"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def ensure_directory(path: Path) -> None:
    """确保目录存在"""
    path.mkdir(parents=True, exist_ok=True)
