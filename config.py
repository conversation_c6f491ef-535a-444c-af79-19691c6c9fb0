"""
配置文件
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据目录
DATA_DIR = PROJECT_ROOT / "data"
OUTPUT_DIR = DATA_DIR / "output"

# GitHub配置
GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")  # 可选，用于提高API限制
GITHUB_SEARCH_KEYWORDS = [
    "surge rules",
    "surge list",
    "gfwlist",
    "china domains",
    "proxy rules",
    "surge conf",
    "clash rules"
]

# 规则分类配置
RULE_CATEGORIES = {
    "foreign": {
        "name": "国外域名",
        "keywords": ["google", "facebook", "twitter", "youtube", "netflix", "amazon"],
        "output_file": "foreign_domains.list"
    },
    "gfw": {
        "name": "GFW阻挡域名",
        "keywords": ["gfw", "blocked", "censored"],
        "output_file": "gfw_domains.list"
    },
    "china": {
        "name": "国内域名",
        "keywords": ["china", "cn", "baidu", "tencent", "alibaba"],
        "output_file": "china_domains.list"
    },
    "china_ip": {
        "name": "国内IP ASN",
        "keywords": ["china ip", "cn ip", "asn"],
        "output_file": "china_ip.list"
    },
    "ai": {
        "name": "AI服务",
        "keywords": ["openai", "chatgpt", "claude", "gemini", "copilot"],
        "output_file": "ai_services.list"
    },
    "telegram": {
        "name": "Telegram",
        "keywords": ["telegram", "t.me"],
        "output_file": "telegram.list"
    }
}

# 输出格式配置
OUTPUT_FORMAT = {
    "header_template": "# {category_name} Rules\n# Generated at {timestamp}\n# Total rules: {count}\n\n",
    "rule_template": "{rule_type},{content},{policy}\n"
}

# 去重配置
DEDUP_CONFIG = {
    "merge_ip_ranges": True,
    "remove_duplicates": True,
    "optimize_rules": True
}

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
