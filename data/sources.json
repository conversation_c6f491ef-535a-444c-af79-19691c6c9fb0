{"github_repositories": [{"name": "Loyalsoldier/surge-rules", "description": "🦄️ 🎃 👻 Surge 规则集(DOMAIN-SET 和 RULE-SET)，兼容 ClashX Pro、Clash for Windows 客户端。", "priority": "high"}, {"name": "Loyalsoldier/clash-rules", "description": "🦄️ 🎃 👻 Clash Premium 规则集(RULE-SET)，兼容 ClashX Pro、Clash for Windows 客户端。", "priority": "high"}, {"name": "blackmatrix7/ios_rule_script", "description": "分流规则、重写写规则及脚本。", "priority": "high"}, {"name": "ACL4SSR/ACL4SSR", "description": "SSR 去广告ACL规则/SS完整GFWList规则/Clash规则碎片，Telegram频道订阅地址", "priority": "medium"}, {"name": "ConnersHua/Profiles", "description": "Clash、Kitsunebi、Quantumult(X)、Shadowrocket、Pepi(ShadowEsocks)、<PERSON>ge 的配置规则文件", "priority": "medium"}, {"name": "lhie1/Rules", "description": "Rules / 规则：Surge / Shadowrocket / Quantumult", "priority": "medium"}, {"name": "sve1r/Rules-For-Quantumult-X", "description": "适用于 Quantumult X 的规则集", "priority": "low"}, {"name": "GeQ1an/Rules", "description": "自用 Clash、Surge 规则集", "priority": "low"}], "search_keywords": ["surge rules", "surge list", "gfwlist", "china domains", "proxy rules", "surge conf", "clash rules", "shadowrocket rules", "quantumult rules", "ios rule script", "telegram ip", "china ip asn", "ai services domains"], "file_patterns": ["*.list", "*.conf", "*.rules", "*gfw*", "*china*", "*proxy*", "*surge*", "*clash*", "*telegram*", "*ai*", "*openai*", "*chatgpt*"], "categories": {"foreign": {"name": "国外域名", "description": "常见的国外网站和服务域名", "policy": "PROXY", "keywords": ["google", "facebook", "twitter", "youtube", "netflix", "amazon", "microsoft", "apple", "github"], "domains": ["google.com", "youtube.com", "facebook.com", "twitter.com", "instagram.com", "netflix.com"]}, "gfw": {"name": "GFW阻挡域名", "description": "被GFW阻挡的域名列表", "policy": "PROXY", "keywords": ["gfw", "blocked", "censored", "firewall"], "domains": ["facebook.com", "twitter.com", "youtube.com", "google.com", "instagram.com"]}, "china": {"name": "国内域名", "description": "中国大陆的网站和服务域名", "policy": "DIRECT", "keywords": ["china", "cn", "baidu", "tencent", "alibaba", "qq", "weibo"], "domains": ["baidu.com", "qq.com", "taobao.com", "weibo.com", "sina.com.cn"]}, "china_ip": {"name": "国内IP ASN", "description": "中国大陆的IP地址段", "policy": "DIRECT", "keywords": ["china ip", "cn ip", "asn", "cernet", "chinanet"], "ip_ranges": ["*******/24", "*********/22", "********/9", "********/10"]}, "ai": {"name": "AI服务", "description": "人工智能相关服务的域名", "policy": "PROXY", "keywords": ["openai", "chatgpt", "claude", "anthropic", "gemini", "bard", "copilot", "midjourney"], "domains": ["openai.com", "claude.ai", "anthropic.com", "midjourney.com", "stability.ai"]}, "telegram": {"name": "Telegram", "description": "Telegram相关的域名和IP", "policy": "PROXY", "keywords": ["telegram", "t.me"], "domains": ["telegram.org", "t.me", "telegram.me", "telegra.ph"], "ip_ranges": ["*************/20", "*************/22", "**********/22"]}}}