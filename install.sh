#!/bin/bash

# Surge Rules Collector 安装脚本

echo "============================================================"
echo "Surge Rules Collector 安装脚本"
echo "============================================================"

# 检查Python版本
echo "检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
    echo "找到Python: $PYTHON_VERSION"
else
    echo "错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查pip
echo "检查pip..."
if python3 -m pip --version &> /dev/null; then
    echo "pip可用"
else
    echo "错误: pip不可用，请检查Python安装"
    exit 1
fi

# 创建虚拟环境（可选）
read -p "是否创建虚拟环境? (y/n): " create_venv
if [[ $create_venv == "y" || $create_venv == "Y" ]]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    
    echo "激活虚拟环境..."
    source venv/bin/activate
    
    echo "虚拟环境已创建并激活"
    echo "要激活虚拟环境，请运行: source venv/bin/activate"
fi

# 安装依赖
echo "安装Python依赖包..."
python3 -m pip install --upgrade pip
python3 -m pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "依赖安装成功！"
else
    echo "依赖安装失败，请检查网络连接和requirements.txt文件"
    exit 1
fi

# 创建必要目录
echo "创建输出目录..."
mkdir -p data/output
mkdir -p logs

# 设置权限
chmod +x main.py
chmod +x demo.py

echo "============================================================"
echo "安装完成！"
echo ""
echo "使用方法:"
echo "1. 运行演示版本: python3 demo.py"
echo "2. 运行完整版本: python3 main.py"
echo "3. 查看帮助: python3 main.py --help"
echo ""
echo "建议设置GitHub Token以避免API限制:"
echo "export GITHUB_TOKEN=your_token_here"
echo ""
echo "输出文件将保存在 data/output/ 目录中"
echo "============================================================"
