#!/usr/bin/env python3
"""
Surge规则收集和整理工具
从GitHub收集分流规则，进行分类、去重和格式化
"""
import logging
import click
from pathlib import Path
from tqdm import tqdm
import time

from config import PROJECT_ROOT, OUTPUT_DIR, GITHUB_TOKEN
from utils.helpers import setup_logging, ensure_directory
from collectors.github_collector import GitHubCollector
from collectors.rule_parser import RuleParser
from collectors.rule_classifier import RuleClassifier
from processors.deduplicator import Deduplicator
from processors.formatter import RuleFormatter

logger = logging.getLogger(__name__)


@click.command()
@click.option('--token', '-t', default=GITHUB_TOKEN, help='GitHub API token')
@click.option('--max-repos', '-r', default=20, help='Maximum repositories to process')
@click.option('--output-dir', '-o', default=str(OUTPUT_DIR), help='Output directory')
@click.option('--log-level', '-l', default='INFO', help='Log level')
@click.option('--log-file', help='Log file path')
@click.option('--dry-run', is_flag=True, help='Dry run mode (no file output)')
def main(token, max_repos, output_dir, log_level, log_file, dry_run):
    """Surge规则收集和整理工具"""
    
    # 设置日志
    log_file_path = Path(log_file) if log_file else None
    setup_logging(log_level, log_file_path)
    
    logger.info("=" * 60)
    logger.info("Surge规则收集工具启动")
    logger.info("=" * 60)
    
    if dry_run:
        logger.info("运行模式: 试运行 (不会保存文件)")
    
    try:
        # 确保输出目录存在
        output_path = Path(output_dir)
        ensure_directory(output_path)
        
        # 初始化组件
        logger.info("初始化组件...")
        collector = GitHubCollector(token)
        parser = RuleParser()
        classifier = RuleClassifier()
        deduplicator = Deduplicator()
        formatter = RuleFormatter(output_path)
        
        # 步骤1: 收集规则
        logger.info("步骤 1/5: 从GitHub收集规则...")
        with tqdm(desc="收集规则", unit="repo") as pbar:
            raw_rules = collector.collect_rules(max_repos)
            pbar.update(1)
        
        if not raw_rules.get('raw_content'):
            logger.error("未找到任何规则内容")
            return
        
        logger.info(f"收集到 {len(raw_rules['raw_content'])} 个规则文件")
        
        # 步骤2: 解析规则
        logger.info("步骤 2/5: 解析规则内容...")
        with tqdm(desc="解析规则", total=len(raw_rules['raw_content'])) as pbar:
            parsed_rules = parser.parse_multiple_sources(raw_rules['raw_content'])
            pbar.update(len(raw_rules['raw_content']))
        
        total_parsed = sum(len(rules) for rules in parsed_rules.values())
        logger.info(f"解析完成，共 {total_parsed} 条规则")
        
        # 步骤3: 分类规则
        logger.info("步骤 3/5: 分类规则...")
        with tqdm(desc="分类规则", unit="rule") as pbar:
            classified_rules = classifier.classify_rules(parsed_rules)
            pbar.update(1)
        
        # 步骤4: 去重和优化
        logger.info("步骤 4/5: 去重和优化...")
        with tqdm(desc="优化规则", unit="category") as pbar:
            optimized_rules = deduplicator.deduplicate_rules(classified_rules)
            pbar.update(len(classified_rules))
        
        # 步骤5: 格式化和保存
        if not dry_run:
            logger.info("步骤 5/5: 格式化和保存规则...")
            with tqdm(desc="保存规则", unit="file") as pbar:
                output_files = formatter.format_and_save_rules(optimized_rules)
                pbar.update(len(output_files))
            
            # 生成统计信息
            stats = {}
            for category, rules in optimized_rules.items():
                if category == 'unclassified':
                    continue
                stats[category] = {
                    'domains': len(rules.get('domains', set())),
                    'domain_suffixes': len(rules.get('domain_suffixes', set())),
                    'domain_keywords': len(rules.get('domain_keywords', set())),
                    'ip_cidrs': len(rules.get('ip_cidrs', set())),
                    'total': sum(len(rule_set) for rule_set in rules.values())
                }
            
            # 生成README
            formatter.generate_readme(output_files, stats)
            
            logger.info("=" * 60)
            logger.info("处理完成！")
            logger.info(f"输出目录: {output_path}")
            logger.info("生成的文件:")
            for category, file_path in output_files.items():
                filename = Path(file_path).name
                rule_count = stats.get(category, {}).get('total', 0)
                logger.info(f"  - {filename}: {rule_count} 条规则")
            logger.info("=" * 60)
        else:
            logger.info("步骤 5/5: 试运行模式，跳过文件保存")
            
            # 显示统计信息
            logger.info("=" * 60)
            logger.info("试运行结果统计:")
            for category, rules in optimized_rules.items():
                if category == 'unclassified':
                    continue
                total = sum(len(rule_set) for rule_set in rules.values())
                if total > 0:
                    logger.info(f"  - {category}: {total} 条规则")
            logger.info("=" * 60)
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}", exc_info=True)
        raise


if __name__ == '__main__':
    main()
