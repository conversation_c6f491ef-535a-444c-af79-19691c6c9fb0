# Surge Rules Collector

一个自动收集和整理Surge分流规则的Python工具，从GitHub上的开源项目中收集规则，进行分类、去重和格式化。

## 功能特性

- 🔍 **自动收集**: 从GitHub搜索和下载分流规则仓库
- 📝 **智能解析**: 支持多种规则格式（Surge、Clash、简单列表等）
- 🏷️ **自动分类**: 将规则分类为国外域名、GFW域名、国内域名、AI服务等
- 🔄 **去重优化**: 自动去重和优化规则，合并IP段
- 📊 **统计报告**: 生成详细的统计报告
- 💾 **标准格式**: 输出标准的Surge格式规则文件

## 支持的规则分类

- **国外域名**: 常见的国外网站和服务
- **GFW阻挡域名**: 被防火墙阻挡的域名
- **国内域名**: 中国大陆的网站和服务
- **国内IP ASN**: 中国大陆的IP地址段
- **AI服务**: OpenAI、Claude、Gemini等AI服务
- **Telegram**: Telegram相关域名和IP

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python main.py
```

### 高级选项

```bash
# 使用GitHub token（推荐，避免API限制）
python main.py --token YOUR_GITHUB_TOKEN

# 指定最大处理仓库数量
python main.py --max-repos 30

# 指定输出目录
python main.py --output-dir ./output

# 设置日志级别
python main.py --log-level DEBUG

# 试运行模式（不保存文件）
python main.py --dry-run

# 保存日志到文件
python main.py --log-file ./logs/collector.log
```

### 获取GitHub Token

1. 访问 [GitHub Settings > Personal access tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token"
3. 选择适当的权限（只需要public_repo权限）
4. 复制生成的token

或者设置环境变量：
```bash
export GITHUB_TOKEN=your_token_here
```

## 输出文件

运行完成后，会在输出目录生成以下文件：

- `foreign_domains.list` - 国外域名规则
- `gfw_domains.list` - GFW阻挡域名规则  
- `china_domains.list` - 国内域名规则
- `china_ip.list` - 国内IP规则
- `ai_services.list` - AI服务规则
- `telegram.list` - Telegram规则
- `all_rules.list` - 所有规则合并文件
- `stats_report.txt` - 统计报告
- `README.md` - 使用说明

## 规则格式

生成的规则采用标准Surge格式：

```
# 域名规则
DOMAIN,example.com,PROXY

# 域名后缀规则  
DOMAIN-SUFFIX,example.com,PROXY

# 域名关键词规则
DOMAIN-KEYWORD,keyword,PROXY

# IP CIDR规则
IP-CIDR,***********/24,DIRECT
```

## 配置文件

### config.py

主要配置选项：

- `GITHUB_SEARCH_KEYWORDS`: 搜索关键词
- `RULE_CATEGORIES`: 规则分类配置
- `OUTPUT_FORMAT`: 输出格式配置
- `DEDUP_CONFIG`: 去重配置

### data/sources.json

数据源配置，包含：

- 推荐的GitHub仓库列表
- 搜索关键词
- 文件匹配模式
- 分类规则定义

## 项目结构

```
surge_rules/
├── main.py                 # 主程序入口
├── config.py              # 配置文件
├── requirements.txt        # 依赖包
├── collectors/            # 收集器模块
│   ├── github_collector.py    # GitHub规则收集
│   ├── rule_parser.py         # 规则解析器
│   └── rule_classifier.py     # 规则分类器
├── processors/            # 处理器模块
│   ├── deduplicator.py        # 去重处理
│   └── formatter.py           # 格式化输出
├── data/                  # 数据目录
│   ├── sources.json           # 数据源配置
│   └── output/               # 输出目录
└── utils/                 # 工具模块
    └── helpers.py             # 辅助函数
```

## 开发说明

### 添加新的规则分类

1. 在 `config.py` 的 `RULE_CATEGORIES` 中添加新分类
2. 在 `collectors/rule_classifier.py` 中添加分类逻辑
3. 在 `processors/formatter.py` 中添加策略映射

### 添加新的规则格式支持

1. 在 `collectors/rule_parser.py` 中添加解析模式
2. 实现对应的解析逻辑

## 注意事项

- GitHub API有速率限制，建议使用token
- 大量仓库处理可能需要较长时间
- 生成的规则仅供参考，请根据实际需要调整

## 免责声明

本工具仅供学习和研究使用，请遵守当地法律法规。使用本工具产生的任何后果由使用者自行承担。

## 许可证

MIT License
