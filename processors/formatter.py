"""
输出格式化器
生成Surge格式的规则文件
"""
import logging
from typing import Dict, Set, List
from datetime import datetime
from pathlib import Path

from config import OUTPUT_DIR, RULE_CATEGORIES, OUTPUT_FORMAT

logger = logging.getLogger(__name__)


class RuleFormatter:
    """规则格式化器"""
    
    def __init__(self, output_dir: Path = None):
        self.output_dir = output_dir or OUTPUT_DIR
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 默认策略映射
        self.policy_mapping = {
            'foreign': 'PROXY',
            'gfw': 'PROXY', 
            'china': 'DIRECT',
            'china_ip': 'DIRECT',
            'ai': 'PROXY',
            'telegram': 'PROXY',
            'unclassified': 'PROXY'
        }
    
    def format_and_save_rules(self, classified_rules: Dict[str, Dict[str, Set[str]]]) -> Dict[str, str]:
        """格式化并保存规则文件"""
        output_files = {}
        stats = {}
        
        for category, rules in classified_rules.items():
            if category == 'unclassified':
                continue  # 跳过未分类的规则
                
            logger.info(f"格式化分类: {category}")
            
            # 生成规则内容
            rule_content, rule_stats = self._format_category_rules(category, rules)
            
            if rule_content:
                # 保存到文件
                filename = self._get_output_filename(category)
                file_path = self.output_dir / filename
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(rule_content)
                
                output_files[category] = str(file_path)
                stats[category] = rule_stats
                
                logger.info(f"已保存 {category} 规则到: {filename} ({rule_stats['total']} 条规则)")
        
        # 生成统计报告
        self._generate_stats_report(stats)
        
        # 生成合并文件
        self._generate_combined_file(classified_rules)
        
        return output_files
    
    def _format_category_rules(self, category: str, rules: Dict[str, Set[str]]) -> tuple:
        """格式化单个分类的规则"""
        policy = self.policy_mapping.get(category, 'PROXY')
        rule_lines = []
        stats = {'domains': 0, 'domain_suffixes': 0, 'domain_keywords': 0, 'ip_cidrs': 0, 'total': 0}
        
        # 处理完整域名
        for domain in sorted(rules.get('domains', set())):
            rule_lines.append(f"DOMAIN,{domain},{policy}")
            stats['domains'] += 1
        
        # 处理域名后缀
        for suffix in sorted(rules.get('domain_suffixes', set())):
            clean_suffix = suffix.lstrip('.')
            rule_lines.append(f"DOMAIN-SUFFIX,{clean_suffix},{policy}")
            stats['domain_suffixes'] += 1
        
        # 处理域名关键词
        for keyword in sorted(rules.get('domain_keywords', set())):
            rule_lines.append(f"DOMAIN-KEYWORD,{keyword},{policy}")
            stats['domain_keywords'] += 1
        
        # 处理IP CIDR
        for ip_cidr in sorted(rules.get('ip_cidrs', set())):
            rule_lines.append(f"IP-CIDR,{ip_cidr},{policy}")
            stats['ip_cidrs'] += 1
        
        stats['total'] = len(rule_lines)
        
        if not rule_lines:
            return "", stats
        
        # 生成文件头部
        category_name = RULE_CATEGORIES.get(category, {}).get('name', category.title())
        header = self._generate_header(category_name, stats['total'])
        
        # 组合内容
        content = header + '\n'.join(rule_lines) + '\n'
        
        return content, stats
    
    def _generate_header(self, category_name: str, rule_count: int) -> str:
        """生成文件头部"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        header = f"""# {category_name} Rules
# Generated at {timestamp}
# Total rules: {rule_count}
# 
# This file contains {category_name.lower()} rules for Surge
# Format: RULE-TYPE,CONTENT,POLICY
#

"""
        return header
    
    def _get_output_filename(self, category: str) -> str:
        """获取输出文件名"""
        return RULE_CATEGORIES.get(category, {}).get('output_file', f'{category}.list')
    
    def _generate_stats_report(self, stats: Dict[str, Dict]) -> None:
        """生成统计报告"""
        report_path = self.output_dir / 'stats_report.txt'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Surge Rules Statistics Report\n")
            f.write(f"# Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            total_rules = 0
            for category, category_stats in stats.items():
                category_name = RULE_CATEGORIES.get(category, {}).get('name', category.title())
                f.write(f"## {category_name}\n")
                f.write(f"- 域名规则: {category_stats['domains']}\n")
                f.write(f"- 域名后缀规则: {category_stats['domain_suffixes']}\n")
                f.write(f"- 域名关键词规则: {category_stats['domain_keywords']}\n")
                f.write(f"- IP CIDR规则: {category_stats['ip_cidrs']}\n")
                f.write(f"- 总计: {category_stats['total']}\n\n")
                total_rules += category_stats['total']
            
            f.write(f"## 总计\n")
            f.write(f"- 所有规则总数: {total_rules}\n")
            f.write(f"- 分类数量: {len(stats)}\n")
        
        logger.info(f"统计报告已保存到: stats_report.txt")
    
    def _generate_combined_file(self, classified_rules: Dict[str, Dict[str, Set[str]]]) -> None:
        """生成合并的规则文件"""
        combined_path = self.output_dir / 'all_rules.list'
        
        with open(combined_path, 'w', encoding='utf-8') as f:
            f.write("# Combined Surge Rules\n")
            f.write(f"# Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("# This file contains all rules combined\n\n")
            
            for category in ['foreign', 'gfw', 'ai', 'telegram', 'china', 'china_ip']:
                if category not in classified_rules:
                    continue
                    
                rules = classified_rules[category]
                if not any(rules.values()):
                    continue
                
                category_name = RULE_CATEGORIES.get(category, {}).get('name', category.title())
                f.write(f"# {category_name} Rules\n")
                
                rule_content, _ = self._format_category_rules(category, rules)
                if rule_content:
                    # 提取规则部分（跳过头部）
                    lines = rule_content.split('\n')
                    rule_lines = [line for line in lines if line and not line.startswith('#')]
                    f.write('\n'.join(rule_lines))
                    f.write('\n\n')
        
        logger.info(f"合并规则文件已保存到: all_rules.list")
    
    def generate_readme(self, output_files: Dict[str, str], stats: Dict[str, Dict]) -> None:
        """生成README文件"""
        readme_path = self.output_dir / 'README.md'
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("# Surge Rules Collection\n\n")
            f.write(f"Generated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 文件说明\n\n")
            f.write("本项目自动收集和整理了各种Surge分流规则，包括：\n\n")
            
            for category, file_path in output_files.items():
                category_name = RULE_CATEGORIES.get(category, {}).get('name', category.title())
                filename = Path(file_path).name
                rule_count = stats.get(category, {}).get('total', 0)
                f.write(f"- **{category_name}** (`{filename}`): {rule_count} 条规则\n")
            
            f.write("\n## 使用方法\n\n")
            f.write("1. 下载对应的规则文件\n")
            f.write("2. 在Surge配置中添加规则引用\n")
            f.write("3. 或者直接复制规则内容到配置文件中\n\n")
            
            f.write("## 规则格式\n\n")
            f.write("所有规则都采用标准的Surge格式：\n")
            f.write("```\n")
            f.write("DOMAIN,example.com,PROXY\n")
            f.write("DOMAIN-SUFFIX,example.com,PROXY\n")
            f.write("DOMAIN-KEYWORD,keyword,PROXY\n")
            f.write("IP-CIDR,***********/24,DIRECT\n")
            f.write("```\n\n")
            
            f.write("## 更新频率\n\n")
            f.write("规则会定期从GitHub上的各个开源项目中收集和更新。\n\n")
            
            f.write("## 免责声明\n\n")
            f.write("本项目仅供学习和研究使用，请遵守当地法律法规。\n")
        
        logger.info(f"README文件已保存到: README.md")
