"""
去重和优化处理器
"""
import logging
from typing import Set, List, Dict
import ipaddress
from collections import defaultdict

logger = logging.getLogger(__name__)


class Deduplicator:
    """去重和优化处理器"""
    
    def __init__(self):
        pass
    
    def deduplicate_rules(self, classified_rules: Dict[str, Dict[str, Set[str]]]) -> Dict[str, Dict[str, Set[str]]]:
        """对分类后的规则进行去重和优化"""
        optimized_rules = {}
        
        for category, rules in classified_rules.items():
            logger.info(f"优化分类: {category}")
            
            optimized_rules[category] = {
                'domains': self._deduplicate_domains(rules.get('domains', set())),
                'domain_suffixes': self._deduplicate_domain_suffixes(rules.get('domain_suffixes', set())),
                'domain_keywords': self._deduplicate_keywords(rules.get('domain_keywords', set())),
                'ip_cidrs': self._optimize_ip_cidrs(rules.get('ip_cidrs', set()))
            }
            
            # 域名和域名后缀的交叉优化
            optimized_rules[category] = self._cross_optimize_domains(optimized_rules[category])
        
        return optimized_rules
    
    def _deduplicate_domains(self, domains: Set[str]) -> Set[str]:
        """去重域名"""
        if not domains:
            return set()
            
        # 转换为小写并去重
        unique_domains = {domain.lower().strip('.') for domain in domains if domain.strip()}
        
        # 移除无效域名
        valid_domains = set()
        for domain in unique_domains:
            if self._is_valid_domain(domain):
                valid_domains.add(domain)
        
        logger.debug(f"域名去重: {len(domains)} -> {len(valid_domains)}")
        return valid_domains
    
    def _deduplicate_domain_suffixes(self, suffixes: Set[str]) -> Set[str]:
        """去重和优化域名后缀"""
        if not suffixes:
            return set()
            
        # 标准化后缀格式
        normalized_suffixes = set()
        for suffix in suffixes:
            suffix = suffix.lower().strip()
            if suffix and self._is_valid_domain(suffix.lstrip('.')):
                # 确保后缀以点开头
                if not suffix.startswith('.'):
                    suffix = '.' + suffix
                normalized_suffixes.add(suffix)
        
        # 移除被其他后缀包含的后缀
        optimized_suffixes = set()
        sorted_suffixes = sorted(normalized_suffixes, key=len)
        
        for suffix in sorted_suffixes:
            is_redundant = False
            for existing in optimized_suffixes:
                if suffix.endswith(existing):
                    is_redundant = True
                    break
            if not is_redundant:
                optimized_suffixes.add(suffix)
        
        logger.debug(f"域名后缀优化: {len(suffixes)} -> {len(optimized_suffixes)}")
        return optimized_suffixes
    
    def _deduplicate_keywords(self, keywords: Set[str]) -> Set[str]:
        """去重关键词"""
        if not keywords:
            return set()
            
        # 转换为小写并去重
        unique_keywords = {keyword.lower().strip() for keyword in keywords if keyword.strip()}
        
        # 移除被其他关键词包含的关键词
        optimized_keywords = set()
        sorted_keywords = sorted(unique_keywords, key=len)
        
        for keyword in sorted_keywords:
            is_redundant = False
            for existing in optimized_keywords:
                if keyword in existing or existing in keyword:
                    # 保留较短的关键词
                    if len(keyword) < len(existing):
                        optimized_keywords.discard(existing)
                        optimized_keywords.add(keyword)
                    is_redundant = True
                    break
            if not is_redundant:
                optimized_keywords.add(keyword)
        
        logger.debug(f"关键词优化: {len(keywords)} -> {len(optimized_keywords)}")
        return optimized_keywords
    
    def _optimize_ip_cidrs(self, ip_cidrs: Set[str]) -> Set[str]:
        """优化IP CIDR规则"""
        if not ip_cidrs:
            return set()
            
        # 解析和验证IP网络
        valid_networks = []
        for cidr in ip_cidrs:
            try:
                network = ipaddress.IPv4Network(cidr, strict=False)
                valid_networks.append(network)
            except:
                logger.debug(f"无效的IP CIDR: {cidr}")
                continue
        
        if not valid_networks:
            return set()
        
        # 按网络地址排序
        valid_networks.sort(key=lambda x: (x.network_address, x.prefixlen))
        
        # 合并重叠的网络
        merged_networks = []
        current_network = valid_networks[0]
        
        for network in valid_networks[1:]:
            if self._networks_can_merge(current_network, network):
                # 合并网络
                current_network = self._merge_networks(current_network, network)
            elif current_network.overlaps(network):
                # 如果重叠，保留更大的网络
                if current_network.supernet_of(network):
                    continue  # 当前网络已包含新网络
                elif network.supernet_of(current_network):
                    current_network = network  # 新网络包含当前网络
                else:
                    # 部分重叠，添加当前网络并开始新的
                    merged_networks.append(current_network)
                    current_network = network
            else:
                # 不重叠，添加当前网络并开始新的
                merged_networks.append(current_network)
                current_network = network
        
        merged_networks.append(current_network)
        
        # 转换回字符串格式
        optimized_cidrs = {str(network) for network in merged_networks}
        
        logger.debug(f"IP CIDR优化: {len(ip_cidrs)} -> {len(optimized_cidrs)}")
        return optimized_cidrs
    
    def _cross_optimize_domains(self, rules: Dict[str, Set[str]]) -> Dict[str, Set[str]]:
        """域名和域名后缀的交叉优化"""
        domains = rules.get('domains', set())
        suffixes = rules.get('domain_suffixes', set())
        
        if not domains or not suffixes:
            return rules
        
        # 移除被域名后缀覆盖的完整域名
        optimized_domains = set()
        for domain in domains:
            is_covered = False
            for suffix in suffixes:
                suffix_clean = suffix.lstrip('.')
                if domain == suffix_clean or domain.endswith('.' + suffix_clean):
                    is_covered = True
                    break
            if not is_covered:
                optimized_domains.add(domain)
        
        removed_count = len(domains) - len(optimized_domains)
        if removed_count > 0:
            logger.debug(f"交叉优化移除了 {removed_count} 个被后缀覆盖的域名")
        
        rules['domains'] = optimized_domains
        return rules
    
    def _is_valid_domain(self, domain: str) -> bool:
        """验证域名格式"""
        if not domain or len(domain) > 253:
            return False
            
        if domain.startswith('.') or domain.endswith('.'):
            domain = domain.strip('.')
            
        if not domain:
            return False
            
        parts = domain.split('.')
        if len(parts) < 2:
            return False
            
        for part in parts:
            if not part or len(part) > 63:
                return False
            if not part.replace('-', '').isalnum():
                return False
            if part.startswith('-') or part.endswith('-'):
                return False
                
        return True
    
    def _networks_can_merge(self, net1: ipaddress.IPv4Network, net2: ipaddress.IPv4Network) -> bool:
        """检查两个网络是否可以合并"""
        # 检查是否相邻且可以合并为更大的网络
        if net1.prefixlen != net2.prefixlen:
            return False
            
        if net1.prefixlen == 0:
            return False
            
        # 检查是否可以合并为上一级网络
        try:
            parent_prefix = net1.prefixlen - 1
            parent1 = ipaddress.IPv4Network(f"{net1.network_address}/{parent_prefix}", strict=False)
            parent2 = ipaddress.IPv4Network(f"{net2.network_address}/{parent_prefix}", strict=False)
            
            return parent1 == parent2 and abs(int(net1.network_address) - int(net2.network_address)) == (1 << (32 - net1.prefixlen))
        except:
            return False
    
    def _merge_networks(self, net1: ipaddress.IPv4Network, net2: ipaddress.IPv4Network) -> ipaddress.IPv4Network:
        """合并两个网络"""
        parent_prefix = net1.prefixlen - 1
        min_address = min(net1.network_address, net2.network_address)
        return ipaddress.IPv4Network(f"{min_address}/{parent_prefix}", strict=False)
