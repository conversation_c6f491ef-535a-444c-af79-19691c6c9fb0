"""
GitHub规则收集器
从GitHub搜索和下载Surge分流规则
"""
import logging
import requests
import time
from typing import List, Dict, Optional
from github import Github
from urllib.parse import urlparse
import re

from config import GITHUB_TOKEN, GITHUB_SEARCH_KEYWORDS

logger = logging.getLogger(__name__)


class GitHubCollector:
    """GitHub规则收集器"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token or GITHUB_TOKEN
        self.github = Github(self.token) if self.token else Github()
        self.session = requests.Session()
        
    def search_repositories(self, keywords: List[str], max_results: int = 50) -> List[Dict]:
        """搜索包含分流规则的仓库"""
        repositories = []
        
        for keyword in keywords:
            try:
                logger.info(f"搜索关键词: {keyword}")
                repos = self.github.search_repositories(
                    query=keyword,
                    sort="stars",
                    order="desc"
                )
                
                count = 0
                for repo in repos:
                    if count >= max_results:
                        break
                        
                    repo_info = {
                        "name": repo.name,
                        "full_name": repo.full_name,
                        "description": repo.description,
                        "stars": repo.stargazers_count,
                        "url": repo.html_url,
                        "clone_url": repo.clone_url,
                        "default_branch": repo.default_branch
                    }
                    repositories.append(repo_info)
                    count += 1
                    
                # 避免API限制
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"搜索仓库时出错: {e}")
                continue
                
        return repositories
    
    def get_rule_files(self, repo_full_name: str) -> List[Dict]:
        """获取仓库中的规则文件"""
        rule_files = []
        
        try:
            repo = self.github.get_repo(repo_full_name)
            contents = repo.get_contents("")
            
            # 常见的规则文件扩展名和名称
            rule_patterns = [
                r'.*\.list$',
                r'.*\.conf$',
                r'.*\.rules$',
                r'.*gfw.*',
                r'.*china.*',
                r'.*proxy.*',
                r'.*surge.*',
                r'.*clash.*'
            ]
            
            def search_files(contents):
                for content in contents:
                    if content.type == "dir":
                        try:
                            sub_contents = repo.get_contents(content.path)
                            search_files(sub_contents)
                        except:
                            continue
                    else:
                        # 检查文件是否匹配规则模式
                        for pattern in rule_patterns:
                            if re.match(pattern, content.name.lower()) or re.match(pattern, content.path.lower()):
                                file_info = {
                                    "name": content.name,
                                    "path": content.path,
                                    "download_url": content.download_url,
                                    "size": content.size,
                                    "repo": repo_full_name
                                }
                                rule_files.append(file_info)
                                break
            
            search_files(contents)
            
        except Exception as e:
            logger.error(f"获取仓库文件时出错 {repo_full_name}: {e}")
            
        return rule_files
    
    def download_file(self, download_url: str) -> Optional[str]:
        """下载文件内容"""
        try:
            response = self.session.get(download_url, timeout=30)
            response.raise_for_status()
            
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            for encoding in encodings:
                try:
                    return response.content.decode(encoding)
                except UnicodeDecodeError:
                    continue
                    
            # 如果所有编码都失败，使用错误处理
            return response.content.decode('utf-8', errors='ignore')
            
        except Exception as e:
            logger.error(f"下载文件失败 {download_url}: {e}")
            return None
    
    def collect_rules(self, max_repos: int = 20) -> Dict[str, List[str]]:
        """收集所有规则"""
        logger.info("开始收集GitHub规则...")
        
        # 搜索仓库
        repositories = self.search_repositories(GITHUB_SEARCH_KEYWORDS, max_repos)
        logger.info(f"找到 {len(repositories)} 个相关仓库")
        
        all_rules = {
            "domain": [],
            "domain_suffix": [],
            "domain_keyword": [],
            "ip_cidr": [],
            "raw_content": []
        }
        
        for repo in repositories[:max_repos]:
            logger.info(f"处理仓库: {repo['full_name']}")
            
            # 获取规则文件
            rule_files = self.get_rule_files(repo['full_name'])
            logger.info(f"找到 {len(rule_files)} 个规则文件")
            
            for file_info in rule_files:
                logger.info(f"下载文件: {file_info['path']}")
                content = self.download_file(file_info['download_url'])
                
                if content:
                    all_rules["raw_content"].append({
                        "repo": repo['full_name'],
                        "file": file_info['path'],
                        "content": content
                    })
                    
                # 避免过于频繁的请求
                time.sleep(0.5)
        
        logger.info("GitHub规则收集完成")
        return all_rules
