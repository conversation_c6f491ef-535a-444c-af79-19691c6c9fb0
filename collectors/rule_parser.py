"""
规则解析器
解析不同格式的Surge分流规则
"""
import re
import logging
from typing import List, Dict, Set, Optional
import ipaddress

logger = logging.getLogger(__name__)


class RuleParser:
    """规则解析器"""
    
    def __init__(self):
        # 编译正则表达式以提高性能
        self.patterns = {
            # Surge格式规则
            'surge_domain': re.compile(r'^DOMAIN,([^,]+)', re.IGNORECASE),
            'surge_domain_suffix': re.compile(r'^DOMAIN-SUFFIX,([^,]+)', re.IGNORECASE),
            'surge_domain_keyword': re.compile(r'^DOMAIN-KEYWORD,([^,]+)', re.IGNORECASE),
            'surge_ip_cidr': re.compile(r'^IP-CIDR,([^,]+)', re.IGNORECASE),
            'surge_ip_cidr6': re.compile(r'^IP-CIDR6,([^,]+)', re.IGNORECASE),
            
            # 简单域名列表
            'simple_domain': re.compile(r'^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'),
            
            # IP地址和CIDR
            'ip_cidr': re.compile(r'^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?:/\d{1,2})?)$'),
            'ipv6_cidr': re.compile(r'^([0-9a-fA-F:]+(?:/\d{1,3})?)$'),
            
            # 注释和空行
            'comment': re.compile(r'^\s*[#;!]'),
            'empty': re.compile(r'^\s*$'),
            
            # 特殊格式
            'hosts_format': re.compile(r'^(?:0\.0\.0\.0|127\.0\.0\.1)\s+([^\s]+)'),
            'dnsmasq_format': re.compile(r'^(?:server|address)=/([^/]+)/'),
        }
    
    def parse_content(self, content: str, source_info: Dict = None) -> Dict[str, Set[str]]:
        """解析规则内容"""
        rules = {
            'domains': set(),
            'domain_suffixes': set(),
            'domain_keywords': set(),
            'ip_cidrs': set(),
            'ipv6_cidrs': set()
        }
        
        if not content:
            return rules
            
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 跳过注释和空行
            if self.patterns['comment'].match(line) or self.patterns['empty'].match(line):
                continue
            
            try:
                self._parse_line(line, rules)
            except Exception as e:
                logger.debug(f"解析行失败 (行 {line_num}): {line[:50]}... - {e}")
                continue
        
        # 记录解析结果
        if source_info:
            logger.info(f"解析完成 {source_info.get('repo', '')}/{source_info.get('file', '')}: "
                       f"域名={len(rules['domains'])}, "
                       f"域名后缀={len(rules['domain_suffixes'])}, "
                       f"关键词={len(rules['domain_keywords'])}, "
                       f"IP={len(rules['ip_cidrs'])}, "
                       f"IPv6={len(rules['ipv6_cidrs'])}")
        
        return rules
    
    def _parse_line(self, line: str, rules: Dict[str, Set[str]]):
        """解析单行规则"""
        # Surge格式规则
        if match := self.patterns['surge_domain'].match(line):
            domain = match.group(1).strip().lower()
            if self._is_valid_domain(domain):
                rules['domains'].add(domain)
            return
            
        if match := self.patterns['surge_domain_suffix'].match(line):
            domain = match.group(1).strip().lower()
            if self._is_valid_domain(domain):
                rules['domain_suffixes'].add(domain)
            return
            
        if match := self.patterns['surge_domain_keyword'].match(line):
            keyword = match.group(1).strip().lower()
            if keyword:
                rules['domain_keywords'].add(keyword)
            return
            
        if match := self.patterns['surge_ip_cidr'].match(line):
            ip_cidr = match.group(1).strip()
            if self._is_valid_ip_cidr(ip_cidr):
                rules['ip_cidrs'].add(ip_cidr)
            return
            
        if match := self.patterns['surge_ip_cidr6'].match(line):
            ipv6_cidr = match.group(1).strip()
            if self._is_valid_ipv6_cidr(ipv6_cidr):
                rules['ipv6_cidrs'].add(ipv6_cidr)
            return
        
        # Hosts格式
        if match := self.patterns['hosts_format'].match(line):
            domain = match.group(1).strip().lower()
            if self._is_valid_domain(domain):
                rules['domains'].add(domain)
            return
        
        # DNSMasq格式
        if match := self.patterns['dnsmasq_format'].match(line):
            domain = match.group(1).strip().lower()
            if self._is_valid_domain(domain):
                rules['domain_suffixes'].add(domain)
            return
        
        # 简单域名列表
        if self.patterns['simple_domain'].match(line):
            domain = line.lower()
            if self._is_valid_domain(domain):
                rules['domain_suffixes'].add(domain)
            return
        
        # IP CIDR
        if self.patterns['ip_cidr'].match(line):
            if self._is_valid_ip_cidr(line):
                rules['ip_cidrs'].add(line)
            return
        
        # IPv6 CIDR
        if self.patterns['ipv6_cidr'].match(line):
            if self._is_valid_ipv6_cidr(line):
                rules['ipv6_cidrs'].add(line)
            return
    
    def _is_valid_domain(self, domain: str) -> bool:
        """验证域名格式"""
        if not domain or len(domain) > 253:
            return False
            
        # 基本格式检查
        if domain.startswith('.') or domain.endswith('.'):
            return False
            
        # 检查是否包含有效字符
        if not re.match(r'^[a-zA-Z0-9.-]+$', domain):
            return False
            
        # 检查每个部分
        parts = domain.split('.')
        if len(parts) < 2:
            return False
            
        for part in parts:
            if not part or len(part) > 63:
                return False
            if part.startswith('-') or part.endswith('-'):
                return False
                
        return True
    
    def _is_valid_ip_cidr(self, ip_cidr: str) -> bool:
        """验证IPv4 CIDR格式"""
        try:
            ipaddress.IPv4Network(ip_cidr, strict=False)
            return True
        except:
            return False
    
    def _is_valid_ipv6_cidr(self, ipv6_cidr: str) -> bool:
        """验证IPv6 CIDR格式"""
        try:
            ipaddress.IPv6Network(ipv6_cidr, strict=False)
            return True
        except:
            return False
    
    def parse_multiple_sources(self, sources: List[Dict]) -> Dict[str, Set[str]]:
        """解析多个数据源"""
        combined_rules = {
            'domains': set(),
            'domain_suffixes': set(),
            'domain_keywords': set(),
            'ip_cidrs': set(),
            'ipv6_cidrs': set()
        }
        
        for source in sources:
            content = source.get('content', '')
            source_info = {
                'repo': source.get('repo', ''),
                'file': source.get('file', '')
            }
            
            rules = self.parse_content(content, source_info)
            
            # 合并规则
            for key in combined_rules:
                combined_rules[key].update(rules[key])
        
        logger.info(f"总计解析结果: "
                   f"域名={len(combined_rules['domains'])}, "
                   f"域名后缀={len(combined_rules['domain_suffixes'])}, "
                   f"关键词={len(combined_rules['domain_keywords'])}, "
                   f"IP={len(combined_rules['ip_cidrs'])}, "
                   f"IPv6={len(combined_rules['ipv6_cidrs'])}")
        
        return combined_rules
