"""
规则分类器
根据规则内容进行分类
"""
import re
import logging
from typing import Dict, Set, List
import ipaddress

logger = logging.getLogger(__name__)


class RuleClassifier:
    """规则分类器"""
    
    def __init__(self):
        # 预定义的分类规则
        self.classification_rules = {
            'foreign': {
                'domains': {
                    'google.com', 'youtube.com', 'facebook.com', 'twitter.com', 'instagram.com',
                    'netflix.com', 'amazon.com', 'microsoft.com', 'apple.com', 'github.com',
                    'reddit.com', 'wikipedia.org', 'linkedin.com', 'pinterest.com', 'tumblr.com',
                    'dropbox.com', 'spotify.com', 'twitch.tv', 'discord.com', 'slack.com'
                },
                'keywords': [
                    'google', 'youtube', 'facebook', 'twitter', 'instagram', 'netflix',
                    'amazon', 'microsoft', 'apple', 'github', 'reddit', 'wikipedia',
                    'linkedin', 'pinterest', 'tumblr', 'dropbox', 'spotify', 'twitch',
                    'discord', 'slack', 'whatsapp', 'telegram'
                ],
                'suffixes': [
                    '.google.com', '.youtube.com', '.facebook.com', '.twitter.com',
                    '.instagram.com', '.netflix.com', '.amazon.com', '.microsoft.com',
                    '.apple.com', '.github.com'
                ]
            },
            'gfw': {
                'domains': {
                    'facebook.com', 'twitter.com', 'youtube.com', 'google.com',
                    'instagram.com', 'whatsapp.com', 'telegram.org', 't.me',
                    'wikipedia.org', 'blogspot.com', 'wordpress.com'
                },
                'keywords': [
                    'facebook', 'twitter', 'youtube', 'google', 'instagram',
                    'whatsapp', 'telegram', 'wikipedia', 'blogspot', 'wordpress',
                    'github', 'dropbox', 'onedrive'
                ]
            },
            'china': {
                'domains': {
                    'baidu.com', 'qq.com', 'taobao.com', 'tmall.com', 'jd.com',
                    'weibo.com', 'sina.com.cn', 'sohu.com', '163.com', '126.com',
                    'alipay.com', 'wechat.com', 'tencent.com', 'alibaba.com',
                    'douban.com', 'zhihu.com', 'bilibili.com', 'youku.com'
                },
                'keywords': [
                    'baidu', 'qq', 'taobao', 'tmall', 'jd', 'weibo', 'sina',
                    'sohu', '163', '126', 'alipay', 'wechat', 'tencent',
                    'alibaba', 'douban', 'zhihu', 'bilibili', 'youku'
                ],
                'tlds': ['.cn', '.com.cn', '.net.cn', '.org.cn', '.gov.cn', '.edu.cn']
            },
            'ai': {
                'domains': {
                    'openai.com', 'chat.openai.com', 'api.openai.com',
                    'claude.ai', 'anthropic.com', 'gemini.google.com',
                    'bard.google.com', 'copilot.microsoft.com', 'github.com/features/copilot',
                    'midjourney.com', 'stability.ai', 'huggingface.co',
                    'replicate.com', 'runpod.io', 'cohere.ai'
                },
                'keywords': [
                    'openai', 'chatgpt', 'claude', 'anthropic', 'gemini', 'bard',
                    'copilot', 'midjourney', 'stability', 'huggingface',
                    'replicate', 'runpod', 'cohere', 'ai', 'gpt'
                ]
            },
            'telegram': {
                'domains': {
                    'telegram.org', 't.me', 'telegram.me', 'telegra.ph',
                    'tdesktop.com', 'telegram.dog'
                },
                'keywords': ['telegram', 't.me'],
                'ip_ranges': [
                    '*************/20', '*************/22', '*************/22',
                    '*************/22', '**********/22', '**********/22',
                    '***********/22', '***********/22', '***********/22',
                    '***********/20'
                ]
            }
        }
        
        # 中国IP段 (部分常见的)
        self.china_ip_ranges = [
            '*******/24', '*******/23', '*******/21', '********/19',
            '*********/22', '*********/20', '********/18',
            '********/9', '********/10', '********/11',
            '********/8', '********/9', '********/10',
            '60.0.0.0/8', '********/10', '*********/10',
            '*********/11', '*********/8', '110.0.0.0/10',
            '*********/10', '*********/12', '*********/11',
            '*********/10', '*********/11', '*********/10',
            '*********/11', '*********/11', '*********/11',
            '120.0.0.0/10', '*********/11', '*********/11',
            '*********/11', '*********/10', '*********/11'
        ]
    
    def classify_rules(self, rules: Dict[str, Set[str]]) -> Dict[str, Dict[str, Set[str]]]:
        """对规则进行分类"""
        classified = {
            'foreign': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
            'gfw': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
            'china': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
            'ai': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
            'telegram': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
            'china_ip': {'ip_cidrs': set()},
            'unclassified': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()}
        }
        
        # 分类域名
        self._classify_domains(rules.get('domains', set()), classified)
        self._classify_domain_suffixes(rules.get('domain_suffixes', set()), classified)
        self._classify_domain_keywords(rules.get('domain_keywords', set()), classified)
        
        # 分类IP
        self._classify_ip_cidrs(rules.get('ip_cidrs', set()), classified)
        self._classify_ipv6_cidrs(rules.get('ipv6_cidrs', set()), classified)
        
        # 记录分类结果
        for category, category_rules in classified.items():
            total = sum(len(rule_set) for rule_set in category_rules.values())
            if total > 0:
                logger.info(f"{category}: {total} 条规则")
        
        return classified
    
    def _classify_domains(self, domains: Set[str], classified: Dict):
        """分类域名"""
        for domain in domains:
            category = self._get_domain_category(domain)
            classified[category]['domains'].add(domain)
    
    def _classify_domain_suffixes(self, suffixes: Set[str], classified: Dict):
        """分类域名后缀"""
        for suffix in suffixes:
            category = self._get_domain_category(suffix)
            classified[category]['domain_suffixes'].add(suffix)
    
    def _classify_domain_keywords(self, keywords: Set[str], classified: Dict):
        """分类域名关键词"""
        for keyword in keywords:
            category = self._get_keyword_category(keyword)
            classified[category]['domain_keywords'].add(keyword)
    
    def _classify_ip_cidrs(self, ip_cidrs: Set[str], classified: Dict):
        """分类IP CIDR"""
        for ip_cidr in ip_cidrs:
            category = self._get_ip_category(ip_cidr)
            if category == 'china_ip':
                classified['china_ip']['ip_cidrs'].add(ip_cidr)
            elif category == 'telegram':
                classified['telegram']['ip_cidrs'].add(ip_cidr)
            else:
                classified[category]['ip_cidrs'].add(ip_cidr)
    
    def _classify_ipv6_cidrs(self, ipv6_cidrs: Set[str], classified: Dict):
        """分类IPv6 CIDR"""
        for ipv6_cidr in ipv6_cidrs:
            # IPv6暂时归类为未分类
            classified['unclassified']['ip_cidrs'].add(ipv6_cidr)
    
    def _get_domain_category(self, domain: str) -> str:
        """获取域名分类"""
        domain = domain.lower().strip('.')
        
        # 检查完全匹配
        for category, rules in self.classification_rules.items():
            if domain in rules.get('domains', set()):
                return category
        
        # 检查后缀匹配
        for category, rules in self.classification_rules.items():
            for suffix in rules.get('suffixes', []):
                if domain.endswith(suffix):
                    return category
        
        # 检查关键词匹配
        for category, rules in self.classification_rules.items():
            for keyword in rules.get('keywords', []):
                if keyword in domain:
                    return category
        
        # 检查中国域名
        if any(domain.endswith(tld) for tld in self.classification_rules['china']['tlds']):
            return 'china'
        
        return 'unclassified'
    
    def _get_keyword_category(self, keyword: str) -> str:
        """获取关键词分类"""
        keyword = keyword.lower()
        
        for category, rules in self.classification_rules.items():
            if keyword in rules.get('keywords', []):
                return category
        
        return 'unclassified'
    
    def _get_ip_category(self, ip_cidr: str) -> str:
        """获取IP分类"""
        try:
            ip_network = ipaddress.IPv4Network(ip_cidr, strict=False)
            
            # 检查Telegram IP
            for tg_range in self.classification_rules['telegram']['ip_ranges']:
                if ip_network.overlaps(ipaddress.IPv4Network(tg_range)):
                    return 'telegram'
            
            # 检查中国IP
            for china_range in self.china_ip_ranges:
                if ip_network.overlaps(ipaddress.IPv4Network(china_range)):
                    return 'china_ip'
            
        except:
            pass
        
        return 'unclassified'
