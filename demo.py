#!/usr/bin/env python3
"""
Surge规则收集工具演示版本
不依赖外部库的简化版本
"""
import sys
import json
from pathlib import Path

def demo_rule_collection():
    """演示规则收集功能"""
    print("=" * 60)
    print("Surge规则收集工具 - 演示版本")
    print("=" * 60)
    
    # 模拟收集到的规则数据
    demo_rules = {
        'domains': {
            'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
            'baidu.com', 'qq.com', 'weibo.com', 'openai.com', 'claude.ai',
            'telegram.org', 't.me'
        },
        'domain_suffixes': {
            'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
            'instagram.com', 'netflix.com', 'amazon.com', 'microsoft.com',
            'baidu.com', 'qq.com', 'taobao.com', 'weibo.com', 'sina.com.cn',
            'openai.com', 'anthropic.com', 'midjourney.com',
            'telegram.org', 'telegram.me'
        },
        'domain_keywords': {
            'google', 'facebook', 'twitter', 'youtube', 'netflix',
            'baidu', 'qq', 'weibo', 'taobao',
            'openai', 'chatgpt', 'claude', 'anthropic',
            'telegram'
        },
        'ip_cidrs': {
            '*******/32', '*******/32',  # Google DNS
            '*******/32', '*******/32',  # Cloudflare DNS
            '*************/20', '*************/22',  # Telegram
            '**********/22', '**********/22',  # Telegram
            '***************/32',  # 国内DNS
            '*********/32'  # 阿里DNS
        }
    }
    
    print(f"步骤 1: 模拟收集规则...")
    print(f"  - 域名: {len(demo_rules['domains'])} 个")
    print(f"  - 域名后缀: {len(demo_rules['domain_suffixes'])} 个")
    print(f"  - 关键词: {len(demo_rules['domain_keywords'])} 个")
    print(f"  - IP CIDR: {len(demo_rules['ip_cidrs'])} 个")
    
    print(f"\n步骤 2: 规则分类...")
    
    # 简化的分类逻辑
    classified = {
        'foreign': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
        'gfw': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
        'china': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
        'ai': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()},
        'telegram': {'domains': set(), 'domain_suffixes': set(), 'domain_keywords': set(), 'ip_cidrs': set()}
    }
    
    # 分类规则
    foreign_keywords = ['google', 'facebook', 'twitter', 'youtube', 'netflix', 'amazon', 'microsoft']
    gfw_keywords = ['google', 'facebook', 'twitter', 'youtube']
    china_keywords = ['baidu', 'qq', 'weibo', 'taobao', 'sina']
    ai_keywords = ['openai', 'chatgpt', 'claude', 'anthropic', 'midjourney']
    telegram_keywords = ['telegram']
    
    # 分类域名
    for domain in demo_rules['domains']:
        if any(keyword in domain for keyword in ai_keywords):
            classified['ai']['domains'].add(domain)
        elif any(keyword in domain for keyword in telegram_keywords):
            classified['telegram']['domains'].add(domain)
        elif any(keyword in domain for keyword in china_keywords) or domain.endswith('.cn'):
            classified['china']['domains'].add(domain)
        elif any(keyword in domain for keyword in gfw_keywords):
            classified['gfw']['domains'].add(domain)
        elif any(keyword in domain for keyword in foreign_keywords):
            classified['foreign']['domains'].add(domain)
    
    # 分类域名后缀
    for suffix in demo_rules['domain_suffixes']:
        if any(keyword in suffix for keyword in ai_keywords):
            classified['ai']['domain_suffixes'].add(suffix)
        elif any(keyword in suffix for keyword in telegram_keywords):
            classified['telegram']['domain_suffixes'].add(suffix)
        elif any(keyword in suffix for keyword in china_keywords) or suffix.endswith('.cn'):
            classified['china']['domain_suffixes'].add(suffix)
        elif any(keyword in suffix for keyword in gfw_keywords):
            classified['gfw']['domain_suffixes'].add(suffix)
        elif any(keyword in suffix for keyword in foreign_keywords):
            classified['foreign']['domain_suffixes'].add(suffix)
    
    # 分类关键词
    for keyword in demo_rules['domain_keywords']:
        if keyword in ai_keywords:
            classified['ai']['domain_keywords'].add(keyword)
        elif keyword in telegram_keywords:
            classified['telegram']['domain_keywords'].add(keyword)
        elif keyword in china_keywords:
            classified['china']['domain_keywords'].add(keyword)
        elif keyword in gfw_keywords:
            classified['gfw']['domain_keywords'].add(keyword)
        elif keyword in foreign_keywords:
            classified['foreign']['domain_keywords'].add(keyword)
    
    # 分类IP
    telegram_ips = ['*************/20', '*************/22', '**********/22', '**********/22']
    china_ips = ['***************/32', '*********/32']
    
    for ip in demo_rules['ip_cidrs']:
        if ip in telegram_ips:
            classified['telegram']['ip_cidrs'].add(ip)
        elif ip in china_ips:
            classified['china']['ip_cidrs'].add(ip)
        else:
            classified['foreign']['ip_cidrs'].add(ip)
    
    # 显示分类结果
    for category, rules in classified.items():
        total = sum(len(rule_set) for rule_set in rules.values())
        if total > 0:
            print(f"  - {category}: {total} 条规则")
    
    print(f"\n步骤 3: 生成规则文件...")
    
    # 确保输出目录存在
    output_dir = Path("data/output")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 策略映射
    policy_mapping = {
        'foreign': 'PROXY',
        'gfw': 'PROXY',
        'china': 'DIRECT',
        'ai': 'PROXY',
        'telegram': 'PROXY'
    }
    
    # 生成规则文件
    for category, rules in classified.items():
        total = sum(len(rule_set) for rule_set in rules.values())
        if total == 0:
            continue
            
        policy = policy_mapping.get(category, 'PROXY')
        filename = f"{category}.list"
        filepath = output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# {category.title()} Rules\n")
            f.write(f"# Generated by Surge Rules Collector\n")
            f.write(f"# Total rules: {total}\n\n")
            
            # 写入域名规则
            for domain in sorted(rules['domains']):
                f.write(f"DOMAIN,{domain},{policy}\n")
            
            # 写入域名后缀规则
            for suffix in sorted(rules['domain_suffixes']):
                f.write(f"DOMAIN-SUFFIX,{suffix},{policy}\n")
            
            # 写入关键词规则
            for keyword in sorted(rules['domain_keywords']):
                f.write(f"DOMAIN-KEYWORD,{keyword},{policy}\n")
            
            # 写入IP规则
            for ip in sorted(rules['ip_cidrs']):
                f.write(f"IP-CIDR,{ip},{policy}\n")
        
        print(f"  - {filename}: {total} 条规则")
    
    print(f"\n步骤 4: 生成统计报告...")
    
    # 生成统计报告
    stats_file = output_dir / "stats_report.txt"
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write("# Surge Rules Statistics Report\n")
        f.write("# Generated by Demo Version\n\n")
        
        total_rules = 0
        for category, rules in classified.items():
            category_total = sum(len(rule_set) for rule_set in rules.values())
            if category_total == 0:
                continue
                
            f.write(f"## {category.title()}\n")
            f.write(f"- 域名规则: {len(rules['domains'])}\n")
            f.write(f"- 域名后缀规则: {len(rules['domain_suffixes'])}\n")
            f.write(f"- 域名关键词规则: {len(rules['domain_keywords'])}\n")
            f.write(f"- IP CIDR规则: {len(rules['ip_cidrs'])}\n")
            f.write(f"- 总计: {category_total}\n\n")
            total_rules += category_total
        
        f.write(f"## 总计\n")
        f.write(f"- 所有规则总数: {total_rules}\n")
    
    print(f"  - stats_report.txt: 统计报告")
    
    print(f"\n=" * 60)
    print(f"演示完成！")
    print(f"输出目录: {output_dir}")
    print(f"总共生成了 {len([f for f in output_dir.glob('*.list')])} 个规则文件")
    print(f"=" * 60)
    
    # 显示一些示例规则
    print(f"\n示例规则内容:")
    for category in ['foreign', 'china', 'ai', 'telegram']:
        filepath = output_dir / f"{category}.list"
        if filepath.exists():
            print(f"\n{category}.list 前5行:")
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:8]):
                    if line.strip() and not line.startswith('#'):
                        print(f"  {line.strip()}")
                        if i >= 4:  # 只显示前5个非注释行
                            break

if __name__ == '__main__':
    demo_rule_collection()
