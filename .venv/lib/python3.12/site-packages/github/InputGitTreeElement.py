############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>                                  #
# Copyright 2024 <PERSON> <<EMAIL>>                      #
# Copyright 2024 Ji<PERSON> <6035284+<PERSON><PERSON>@users.noreply.github.com>        #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import Any

from github.GithubObject import NotSet, Opt, is_defined, is_optional


class InputGitTreeElement:
    """
    This class represents InputGitTreeElements.
    """

    def __init__(
        self,
        path: str,
        mode: str,
        type: str,
        content: Opt[str] = NotSet,
        sha: Opt[str | None] = NotSet,
    ):
        assert isinstance(path, str), path
        assert isinstance(mode, str), mode
        assert isinstance(type, str), type
        assert is_optional(content, str), content
        assert sha is None or is_optional(sha, str), sha
        self.__path = path
        self.__mode = mode
        self.__type = type
        self.__content = content
        self.__sha: Opt[str] | None = sha

    @property
    def _identity(self) -> dict[str, Any]:
        identity: dict[str, Any] = {
            "path": self.__path,
            "mode": self.__mode,
            "type": self.__type,
        }
        if is_defined(self.__sha):
            identity["sha"] = self.__sha
        if is_defined(self.__content):
            identity["content"] = self.__content
        return identity
