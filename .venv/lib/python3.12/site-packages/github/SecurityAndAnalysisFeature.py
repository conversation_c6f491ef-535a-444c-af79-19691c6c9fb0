############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# <AUTHOR> <EMAIL>                                     #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON><PERSON> <<EMAIL>>                                #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 Wan <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>                                  #
# Copyright 2024 <PERSON> <<EMAIL>>                          #
# Copyright 2024 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from typing import Any, Dict

from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet


class SecurityAndAnalysisFeature(NonCompletableGithubObject):
    """
    This class represents a Security and Analysis feature status.

    The OpenAPI schema can be found at
    - /components/schemas/security-and-analysis/properties/advanced_security
    - /components/schemas/security-and-analysis/properties/dependabot_security_updates
    - /components/schemas/security-and-analysis/properties/secret_scanning
    - /components/schemas/security-and-analysis/properties/secret_scanning_non_provider_patterns
    - /components/schemas/security-and-analysis/properties/secret_scanning_push_protection

    """

    def _initAttributes(self) -> None:
        self._status: Attribute[str] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__({"status": self._status.value})

    @property
    def status(self) -> str:
        return self._status.value

    def _useAttributes(self, attributes: Dict[str, Any]) -> None:
        if "status" in attributes:  # pragma no branch
            self._status = self._makeStringAttribute(attributes["status"])
