############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# <AUTHOR> <EMAIL>                                     #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON><PERSON> <<EMAIL>>                                #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# <AUTHOR> <EMAIL>#
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>                                  #
# Copyright 2024 Enrico <PERSON>ck <<EMAIL>>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from typing import Any, Dict

from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet


class Permissions(NonCompletableGithubObject):
    """
    This class represents Permissions.

    The OpenAPI schema can be found at
    - /components/schemas/collaborator/properties/permissions
    - /components/schemas/full-repository/properties/permissions
    - /components/schemas/minimal-repository/properties/permissions
    - /components/schemas/repo-search-result-item/properties/permissions
    - /components/schemas/repository/properties/permissions
    - /components/schemas/team/properties/permissions

    """

    def _initAttributes(self) -> None:
        self._admin: Attribute[bool] = NotSet
        self._maintain: Attribute[bool] = NotSet
        self._pull: Attribute[bool] = NotSet
        self._push: Attribute[bool] = NotSet
        self._triage: Attribute[bool] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__(
            {
                "admin": self._admin.value,
                "maintain": self._maintain.value,
                "pull": self._pull.value,
                "push": self._push.value,
                "triage": self._triage.value,
            }
        )

    @property
    def admin(self) -> bool:
        return self._admin.value

    @property
    def maintain(self) -> bool:
        return self._maintain.value

    @property
    def pull(self) -> bool:
        return self._pull.value

    @property
    def push(self) -> bool:
        return self._push.value

    @property
    def triage(self) -> bool:
        return self._triage.value

    def _useAttributes(self, attributes: Dict[str, Any]) -> None:
        if "admin" in attributes:  # pragma no branch
            self._admin = self._makeBoolAttribute(attributes["admin"])
        if "maintain" in attributes:  # pragma no branch
            self._maintain = self._makeBoolAttribute(attributes["maintain"])
        if "pull" in attributes:  # pragma no branch
            self._pull = self._makeBoolAttribute(attributes["pull"])
        if "push" in attributes:  # pragma no branch
            self._push = self._makeBoolAttribute(attributes["push"])
        if "triage" in attributes:  # pragma no branch
            self._triage = self._makeBoolAttribute(attributes["triage"])
