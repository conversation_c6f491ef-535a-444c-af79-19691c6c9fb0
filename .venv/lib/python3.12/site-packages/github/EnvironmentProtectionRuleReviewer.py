############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 Adam <PERSON>z <<EMAIL>>                           #
# Copyright 2019 <PERSON> <<EMAIL>>                 #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 Wan <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# Copyright 2023 Jirka <PERSON> <6035284+Bo<PERSON>@users.noreply.github.com>        #
# <AUTHOR> <EMAIL>                                  #
# <AUTHOR> <EMAIL>                                     #
# <AUTHOR> <EMAIL>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import Any

import github.NamedUser
import github.Team
from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet


class EnvironmentProtectionRuleReviewer(NonCompletableGithubObject):
    """
    This class represents a reviewer for an EnvironmentProtectionRule.

    The reference can be found here
    https://docs.github.com/en/rest/reference/deployments#environments

    """

    def _initAttributes(self) -> None:
        self._reviewer: Attribute[github.NamedUser.NamedUser | github.Team.Team] = NotSet
        self._type: Attribute[str] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__({"type": self._type.value})

    @property
    def reviewer(self) -> github.NamedUser.NamedUser | github.Team.Team:
        return self._reviewer.value

    @property
    def type(self) -> str:
        return self._type.value

    def _useAttributes(self, attributes: dict[str, Any]) -> None:
        if "reviewer" in attributes and "type" in attributes:  # pragma no branch
            assert attributes["type"] in ("User", "Team")
            if attributes["type"] == "User":
                self._reviewer = self._makeClassAttribute(github.NamedUser.NamedUser, attributes["reviewer"])
            elif attributes["type"] == "Team":
                self._reviewer = self._makeClassAttribute(github.Team.Team, attributes["reviewer"])
        if "type" in attributes:  # pragma no branch
            self._type = self._makeStringAttribute(attributes["type"])


class ReviewerParams:
    """
    This class presents reviewers as can be configured for an Environment.
    """

    def __init__(self, type_: str, id_: int):
        assert isinstance(type_, str) and type_ in ("User", "Team")
        assert isinstance(id_, int)
        self.type = type_
        self.id = id_

    def _asdict(self) -> dict:
        return {
            "type": self.type,
            "id": self.id,
        }
