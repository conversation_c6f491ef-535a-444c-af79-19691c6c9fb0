############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# <AUTHOR> <EMAIL>                                     #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON><PERSON> <<EMAIL>>                                #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                        #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>                                  #
# <AUTHOR> <EMAIL>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import Any

from github.GithubObject import Attribute, CompletableGithubObject, NotSet


class RequiredStatusChecks(CompletableGithubObject):
    """
    This class represents Required Status Checks.

    The reference can be found here
    https://docs.github.com/en/rest/reference/repos#get-status-checks-protection

    The OpenAPI schema can be found at
    - /components/schemas/protected-branch-pull-request-review
    - /components/schemas/protected-branch-required-status-check
    - /components/schemas/status-check-policy

    """

    def _initAttributes(self) -> None:
        self._contexts: Attribute[list[str]] = NotSet
        self._strict: Attribute[bool] = NotSet
        self._url: Attribute[str] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__({"strict": self._strict.value, "url": self._url.value})

    @property
    def contexts(self) -> list[str]:
        self._completeIfNotSet(self._contexts)
        return self._contexts.value

    @property
    def strict(self) -> bool:
        self._completeIfNotSet(self._strict)
        return self._strict.value

    @property
    def url(self) -> str:
        self._completeIfNotSet(self._url)
        return self._url.value

    def _useAttributes(self, attributes: dict[str, Any]) -> None:
        if "contexts" in attributes:  # pragma no branch
            self._contexts = self._makeListOfStringsAttribute(attributes["contexts"])
        if "strict" in attributes:  # pragma no branch
            self._strict = self._makeBoolAttribute(attributes["strict"])
        if "url" in attributes:  # pragma no branch
            self._url = self._makeStringAttribute(attributes["url"])
