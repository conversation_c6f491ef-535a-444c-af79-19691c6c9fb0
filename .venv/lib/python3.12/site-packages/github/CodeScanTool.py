############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# <AUTHOR> <EMAIL>                                     #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON><PERSON> <<EMAIL>>                                #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 Wan <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2022 <PERSON> <<EMAIL>>                     #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>                                  #
# <AUTHOR> <EMAIL>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from typing import Any, Dict

from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet


class CodeScanTool(NonCompletableGithubObject):
    """
    This class represents code scanning tools.

    The reference can be found here
    https://docs.github.com/en/rest/reference/code-scanning.

    """

    def _initAttributes(self) -> None:
        self._guid: Attribute[str] = NotSet
        self._name: Attribute[str] = NotSet
        self._version: Attribute[str] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__(
            {
                "guid": self.guid,
                "name": self.name,
                "version": self.version,
            }
        )

    @property
    def guid(self) -> str:
        return self._guid.value

    @property
    def name(self) -> str:
        return self._name.value

    @property
    def version(self) -> str:
        return self._version.value

    def _useAttributes(self, attributes: Dict[str, Any]) -> None:
        if "guid" in attributes:  # pragma no branch
            self._guid = self._makeStringAttribute(attributes["guid"])
        if "name" in attributes:  # pragma no branch
            self._name = self._makeStringAttribute(attributes["name"])
        if "version" in attributes:  # pragma no branch
            self._version = self._makeStringAttribute(attributes["version"])
