############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 Adam <PERSON>z <<EMAIL>>                           #
# Copyright 2019 <PERSON> <<EMAIL>>                 #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 Wan <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# Copyright 2023 Jirka <PERSON> <6035284+Bo<PERSON>@users.noreply.github.com>        #
# <AUTHOR> <EMAIL>             #
# <AUTHOR> <EMAIL>                       #
# <AUTHOR> <EMAIL>                                  #
# <AUTHOR> <EMAIL>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import Any

import github.NamedUser
from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet


class AdvisoryCreditDetailed(NonCompletableGithubObject):
    """
    This class represents a credit that is assigned to a SecurityAdvisory.

    The reference can be found here
    https://docs.github.com/en/rest/security-advisories/repository-advisories

    The OpenAPI schema can be found at
    - /components/schemas/global-advisory/properties/credits/items
    - /components/schemas/repository-advisory-credit

    """

    def _initAttributes(self) -> None:
        self._state: Attribute[str] = NotSet
        self._type: Attribute[str] = NotSet
        self._user: Attribute[github.NamedUser.NamedUser] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__(
            {
                "user": self.user,
                "type": self.type,
            }
        )

    @property
    def state(self) -> str:
        """
        :type: string
        """
        return self._state.value

    @property
    def type(self) -> str:
        """
        :type: string
        """
        return self._type.value

    @property
    def user(self) -> github.NamedUser.NamedUser:
        """
        :type: :class:`github.NamedUser.NamedUser`
        """
        return self._user.value

    def _useAttributes(self, attributes: dict[str, Any]) -> None:
        if "state" in attributes:  # pragma no branch
            self._state = self._makeStringAttribute(attributes["state"])
        if "type" in attributes:  # pragma no branch
            self._type = self._makeStringAttribute(attributes["type"])
        if "user" in attributes:  # pragma no branch
            self._user = self._makeClassAttribute(github.NamedUser.NamedUser, attributes["user"])
