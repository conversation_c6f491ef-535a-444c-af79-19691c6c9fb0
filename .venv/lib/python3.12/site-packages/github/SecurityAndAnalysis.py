############################ Copyrights and license ############################
#                                                                              #
# Copyright 2012 <PERSON> <<EMAIL>>                 #
# <AUTHOR> <EMAIL>                                      #
# <AUTHOR> <EMAIL>                                     #
# Copyright 2013 <PERSON> <<EMAIL>>                 #
# Copyright 2014 <PERSON> <<EMAIL>>                 #
# Copyright 2016 <PERSON><PERSON> <<EMAIL>>                                #
# Copyright 2016 <PERSON> <<EMAIL>>          #
# Copyright 2018 <PERSON> <<EMAIL>>                                #
# <AUTHOR> <EMAIL>                                      #
# Copyright 2019 <PERSON> <<EMAIL>>                        #
# Copyright 2019 <PERSON> <<EMAIL>>                                #
# Copyright 2020 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2021 <PERSON> <<EMAIL>>                        #
# Copyright 2023 <PERSON> <<EMAIL>>                      #
# Copyright 2023 <PERSON><PERSON> <<EMAIL>>                         #
# <AUTHOR> <EMAIL>                                  #
# <AUTHOR> <EMAIL>                          #
# <AUTHOR> <EMAIL>                      #
# <AUTHOR> <EMAIL>        #
# <AUTHOR> <EMAIL>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import Any

import github.SecurityAndAnalysisFeature
from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet


class SecurityAndAnalysis(NonCompletableGithubObject):
    """
    This class represents Security and Analysis Settings.

    The OpenAPI schema can be found at
    - /components/schemas/security-and-analysis

    """

    def _initAttributes(self) -> None:
        self._advanced_security: Attribute[github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature] = NotSet
        self._dependabot_security_updates: Attribute[
            github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature
        ] = NotSet
        self._secret_scanning: Attribute[github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature] = NotSet
        self._secret_scanning_ai_detection: Attribute[str] = NotSet
        self._secret_scanning_non_provider_patterns: Attribute[
            github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature
        ] = NotSet
        self._secret_scanning_push_protection: Attribute[
            github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature
        ] = NotSet
        self._secret_scanning_validity_checks: Attribute[
            github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature
        ] = NotSet

    def __repr__(self) -> str:
        repr_attributes = {
            "advanced_security": repr(self._advanced_security.value),
            "dependabot_security_updates": repr(self._dependabot_security_updates.value),
            "secret_scanning": repr(self._secret_scanning.value),
            "secret_scanning_non_provider_patterns": repr(self._secret_scanning_non_provider_patterns.value),
            "secret_scanning_push_protection": repr(self._secret_scanning_push_protection.value),
            "secret_scanning_validity_checks": repr(self._secret_scanning_validity_checks.value),
        }

        return self.get__repr__(repr_attributes)

    @property
    def advanced_security(self) -> github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature:
        return self._advanced_security.value

    @property
    def dependabot_security_updates(self) -> github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature:
        return self._dependabot_security_updates.value

    @property
    def secret_scanning(self) -> github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature:
        return self._secret_scanning.value

    @property
    def secret_scanning_ai_detection(self) -> str:
        return self._secret_scanning_ai_detection.value

    @property
    def secret_scanning_non_provider_patterns(self) -> github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature:
        return self._secret_scanning_non_provider_patterns.value

    @property
    def secret_scanning_push_protection(self) -> github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature:
        return self._secret_scanning_push_protection.value

    @property
    def secret_scanning_validity_checks(self) -> github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature:
        return self._secret_scanning_validity_checks.value

    def _useAttributes(self, attributes: dict[str, Any]) -> None:
        if "advanced_security" in attributes:  # pragma no branch
            self._advanced_security = self._makeClassAttribute(
                github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature, attributes["advanced_security"]
            )
        if "dependabot_security_updates" in attributes:  # pragma no branch
            self._dependabot_security_updates = self._makeClassAttribute(
                github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature, attributes["dependabot_security_updates"]
            )
        if "secret_scanning" in attributes:  # pragma no branch
            self._secret_scanning = self._makeClassAttribute(
                github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature, attributes["secret_scanning"]
            )
        if "secret_scanning_ai_detection" in attributes:  # pragma no branch
            self._secret_scanning_ai_detection = self._makeStringAttribute(attributes["secret_scanning_ai_detection"])
        if "secret_scanning_non_provider_patterns" in attributes:  # pragma no branch
            self._secret_scanning_non_provider_patterns = self._makeClassAttribute(
                github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature,
                attributes["secret_scanning_non_provider_patterns"],
            )
        if "secret_scanning_push_protection" in attributes:  # pragma no branch
            self._secret_scanning_push_protection = self._makeClassAttribute(
                github.SecurityAndAnalysisFeature.SecurityAndAnalysisFeature,
                attributes["secret_scanning_push_protection"],
            )
