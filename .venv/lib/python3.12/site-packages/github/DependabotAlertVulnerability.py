############################ Copyrights and license ############################
#                                                                              #
# Copyright 2024 <PERSON> <<EMAIL>>                      #
# Copyright 2024 <PERSON> <<EMAIL>>                         #
# Copyright 2025 <PERSON> <<EMAIL>>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import TYPE_CHECKING, Any

import github.AdvisoryVulnerabilityPackage
from github.GithubObject import Attribute, NonCompletableGithubObject, NotSet

if TYPE_CHECKING:
    from github.AdvisoryVulnerabilityPackage import AdvisoryVulnerabilityPackage


class DependabotAlertVulnerability(NonCompletableGithubObject):
    """
    A vulnerability represented in a Dependabot alert.

    The OpenAPI schema can be found at
    - /components/schemas/dependabot-alert-security-vulnerability

    """

    def _initAttributes(self) -> None:
        self._first_patched_version: Attribute[dict] = NotSet
        self._package: Attribute[AdvisoryVulnerabilityPackage] = NotSet
        self._severity: Attribute[str] = NotSet
        self._vulnerable_version_range: Attribute[str | None] = NotSet

    def __repr__(self) -> str:
        return self.get__repr__({"package": self.package, "severity": self.severity})

    @property
    def first_patched_version(self) -> dict:
        return self._first_patched_version.value

    @property
    def package(self) -> AdvisoryVulnerabilityPackage:
        return self._package.value

    @property
    def severity(self) -> str:
        return self._severity.value

    @property
    def vulnerable_version_range(self) -> str | None:
        return self._vulnerable_version_range.value

    def _useAttributes(self, attributes: dict[str, Any]) -> None:
        if "first_patched_version" in attributes:
            self._first_patched_version = self._makeDictAttribute(
                attributes["first_patched_version"],
            )
        if "package" in attributes:
            self._package = self._makeClassAttribute(
                github.AdvisoryVulnerabilityPackage.AdvisoryVulnerabilityPackage,
                attributes["package"],
            )
        if "severity" in attributes:
            self._severity = self._makeStringAttribute(attributes["severity"])
        if "vulnerable_version_range" in attributes:
            self._vulnerable_version_range = self._makeStringAttribute(attributes["vulnerable_version_range"])
