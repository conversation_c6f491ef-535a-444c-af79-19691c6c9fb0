############################ Copyrights and license ############################
#                                                                              #
# Copyright 2024 <PERSON> <<EMAIL>>                      #
# <AUTHOR> <EMAIL>        #
# Copyright 2024 <PERSON> <<EMAIL>>                         #
# Copyright 2025 <PERSON> <<EMAIL>>                      #
#                                                                              #
# This file is part of PyGithub.                                               #
# http://pygithub.readthedocs.io/                                              #
#                                                                              #
# PyGithub is free software: you can redistribute it and/or modify it under    #
# the terms of the GNU Lesser General Public License as published by the Free  #
# Software Foundation, either version 3 of the License, or (at your option)    #
# any later version.                                                           #
#                                                                              #
# PyGithub is distributed in the hope that it will be useful, but WITHOUT ANY  #
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS    #
# FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License for more #
# details.                                                                     #
#                                                                              #
# You should have received a copy of the GNU Lesser General Public License     #
# along with PyGithub. If not, see <http://www.gnu.org/licenses/>.             #
#                                                                              #
################################################################################

from __future__ import annotations

from typing import Any

from github.DependabotAlert import DependabotAlert
from github.GithubObject import Attribute, NotSet
from github.Repository import Repository


class OrganizationDependabotAlert(DependabotAlert):
    """
    This class represents a Dependabot alert on an organization.

    The reference can be found here
    https://docs.github.com/en/rest/dependabot/alerts#list-dependabot-alerts-for-an-organization

    The OpenAPI schema can be found at
    - /components/schemas/dependabot-alert-with-repository

    """

    def _initAttributes(self) -> None:
        super()._initAttributes()
        self._repository: Attribute[Repository] = NotSet

    @property
    def repository(self) -> Repository:
        return self._repository.value

    def _useAttributes(self, attributes: dict[str, Any]) -> None:
        super()._useAttributes(attributes)
        if "repository" in attributes:
            self._repository = self._makeClassAttribute(Repository, attributes["repository"])
